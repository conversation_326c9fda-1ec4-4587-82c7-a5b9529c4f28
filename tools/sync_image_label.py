import os
import argparse
from pathlib import Path


def get_file_mapping(image_dir, label_dir, image_ext='.jpg', label_ext='.txt'):
    """
    获取图像和标签文件的映射关系
    
    Args:
        image_dir: 图像文件夹路径
        label_dir: 标签文件夹路径
        image_ext: 图像文件扩展名
        label_ext: 标签文件扩展名
    
    Returns:
        tuple: (image_files, label_files, image_basenames, label_basenames)
    """
    image_files = []
    label_files = []
    
    # 获取所有图像文件
    if os.path.exists(image_dir):
        image_files = [f for f in os.listdir(image_dir) if f.lower().endswith(image_ext.lower())]
    
    # 获取所有标签文件
    if os.path.exists(label_dir):
        label_files = [f for f in os.listdir(label_dir) if f.lower().endswith(label_ext.lower())]
    
    # 获取基础文件名（不含扩展名）
    image_basenames = {os.path.splitext(f)[0] for f in image_files}
    label_basenames = {os.path.splitext(f)[0] for f in label_files}
    
    return image_files, label_files, image_basenames, label_basenames


def sync_folders(image_dir, label_dir, image_ext='.jpg', label_ext='.txt', dry_run=False):
    """
    同步图像和标签文件夹，删除没有对应图像的标签文件
    
    Args:
        image_dir: 图像文件夹路径
        label_dir: 标签文件夹路径
        image_ext: 图像文件扩展名
        label_ext: 标签文件扩展名
        dry_run: 是否只是预览而不实际删除
    """
    print(f"图像文件夹: {image_dir}")
    print(f"标签文件夹: {label_dir}")
    print(f"图像扩展名: {image_ext}")
    print(f"标签扩展名: {label_ext}")
    print("-" * 50)
    
    # 检查文件夹是否存在
    if not os.path.exists(image_dir):
        print(f"错误: 图像文件夹不存在: {image_dir}")
        return
    
    if not os.path.exists(label_dir):
        print(f"错误: 标签文件夹不存在: {label_dir}")
        return
    
    # 获取文件映射
    image_files, label_files, image_basenames, label_basenames = get_file_mapping(
        image_dir, label_dir, image_ext, label_ext
    )
    
    print(f"图像文件数量: {len(image_files)}")
    print(f"标签文件数量: {len(label_files)}")
    
    # 找出需要删除的标签文件（没有对应图像的标签文件）
    orphaned_labels = []
    for label_file in label_files:
        basename = os.path.splitext(label_file)[0]
        if basename not in image_basenames:
            orphaned_labels.append(label_file)
    
    # 找出缺少标签的图像文件（有图像但没有标签的文件）
    missing_labels = []
    for image_file in image_files:
        basename = os.path.splitext(image_file)[0]
        if basename not in label_basenames:
            missing_labels.append(image_file)
    
    print(f"\n分析结果:")
    print(f"需要删除的孤立标签文件: {len(orphaned_labels)}")
    print(f"缺少标签的图像文件: {len(missing_labels)}")
    
    # 显示需要删除的文件
    if orphaned_labels:
        print(f"\n将要删除的标签文件:")
        for i, label_file in enumerate(orphaned_labels, 1):
            print(f"  {i:3d}. {label_file}")
    
    # 显示缺少标签的图像文件
    if missing_labels:
        print(f"\n缺少标签的图像文件:")
        for i, image_file in enumerate(missing_labels, 1):
            print(f"  {i:3d}. {image_file}")
    
    # 执行删除操作
    if orphaned_labels:
        if dry_run:
            print(f"\n[预览模式] 将删除 {len(orphaned_labels)} 个孤立的标签文件")
        else:
            print(f"\n开始删除 {len(orphaned_labels)} 个孤立的标签文件...")
            deleted_count = 0
            for label_file in orphaned_labels:
                label_path = os.path.join(label_dir, label_file)
                try:
                    os.remove(label_path)
                    deleted_count += 1
                    print(f"  已删除: {label_file}")
                except Exception as e:
                    print(f"  删除失败: {label_file} - {e}")
            
            print(f"\n删除完成! 成功删除 {deleted_count} 个文件")
    else:
        print(f"\n没有需要删除的孤立标签文件，文件夹已同步!")
    
    # 最终统计
    remaining_labels = len(label_files) - len(orphaned_labels)
    print(f"\n最终统计:")
    print(f"图像文件: {len(image_files)}")
    print(f"标签文件: {remaining_labels} (删除前: {len(label_files)})")
    print(f"匹配的文件对: {len(image_basenames & label_basenames)}")


def main():
    parser = argparse.ArgumentParser(description='同步图像和标签文件夹，删除没有对应图像的标签文件')
    parser.add_argument('image_dir', help='图像文件夹路径')
    parser.add_argument('label_dir', help='标签文件夹路径')
    parser.add_argument('--image-ext', default='.jpg', help='图像文件扩展名 (默认: .jpg)')
    parser.add_argument('--label-ext', default='.txt', help='标签文件扩展名 (默认: .txt)')
    parser.add_argument('--dry-run', action='store_true', help='预览模式，不实际删除文件')
    
    args = parser.parse_args()
    
    sync_folders(
        image_dir=args.image_dir,
        label_dir=args.label_dir,
        image_ext=args.image_ext,
        label_ext=args.label_ext,
        dry_run=args.dry_run
    )


if __name__ == '__main__':
    # 如果直接运行脚本，可以在这里设置默认路径进行测试
    if len(os.sys.argv) == 1:
        # 默认测试路径
        image_dir = '/Users/<USER>/Downloads/temp_aec_pack_0805_20250805_131300/images'
        label_dir = '/Users/<USER>/Downloads/temp_aec_pack_0805_20250805_131300/labels'
        
        print("使用默认路径进行测试:")
        sync_folders(image_dir, label_dir, dry_run=True)
    else:
        main()
