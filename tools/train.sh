yolo task=detect mode=train model=yolov8n.pt data=/Users/<USER>/Documents/Workspace/line_detect/test001/test001.yaml epochs=100 imgsz=640 batch=16 project=./YOLO_outputs name=my_experiment+$(date +%Y%m%d_%H%M%S)
# --weights yolov8n.pt  # 使用预训练的 YOLOv8n 模型
# --data /Users/<USER>/Documents/Workspace/line_detect/test001/test001.yaml
# --epochs 100  # 训练周期数
# --imgsz 640  # 输入图像大小
# --batch 16  # 批处理大小
# --project ./YOLO_outputs  # 输出目录
# --name my_experiment+$(date +%Y%m%d_%H%M%S)  # 实验名称，包含时间戳
# yolo task=detect mode=predict model=YOLO_outputs/my_experiment_20250630_112229/weights/best.pt source=/Users/<USER>/Documents/Workspace/line_detect/projects/test002/dataset/images/val
# yolo task=classify mode=train model=yolov8n-cls.pt data=路径/到/数据集 epochs=100 imgsz=224
# YOLO_outputs/my_experiment_20250630_112229/weights/best.pt


yolo task=detect mode=predict model=/Users/<USER>/Documents/Workspace/Models/T68ZJ/t68zj_0723_1145_best.onnx source=/Users/<USER>/Downloads/images_202507231157 project=./YOLO_outputs name=my_predict_$(date +%Y%m%d_%H%M%S)
yolo task=detect mode=predict model=/Users/<USER>/Documents/yolov11n_200_best.pt source=/Users/<USER>/Documents/Workspace/line_detect/projects/test002/dataset/images/val project=./YOLO_outputs name=yolov11n_200_best
yolo task=segment mode=predict model=/Users/<USER>/Downloads/yolo_seg_best.pt source=/Users/<USER>/Downloads/cng2_save/cok2_save project=./YOLO_outputs/predict name=my_predict_$(date +%Y%m%d_%H%M%S)

yolo task=obb mode=predict model=/Users/<USER>/Downloads/yolo_pin_obb_best.pt source=/Users/<USER>/Downloads/cng2_save/cok2_save project=./YOLO_outputs/predict name=my_predict_$(date +%Y%m%d_%H%M%S) conf=0.5 iou=0.6

yolo task=segment mode=train model=yolo11n-seg.pt data=/data/train/projects/pin_detect_seg/pin_detect_seg.yaml epochs=300 imgsz=640 batch=16 project=/data/train/YOLO_outputs name=my_experiment_$(date +%Y%m%d%H%M%S)

yolo export model=/Users/<USER>/Documents/Workspace/Models/AEC_Pack/PACK_AOI_best.pt format=onnx
yolo export model=/Users/<USER>/Documents/Workspace/Models/T68ZJ/t68zj_0723_1145_best.pt format=onnx
yolo export model=/Users/<USER>/Documents/Workspace/Models/T68ZJ/t68zj_0722_2046_dataset_best.pt format=onnx

yolo task=obb mode=train model=yolo11n-obb.pt data=/data/train/projects/pin_detect_0722_dataset/pin_detect_0722_dataset.yaml epochs=300 imgsz=640 batch=16 project=/data/train/YOLO_outputs name=my_experiment_$(date +%Y%m%d%H%M%S)
# 推理
yolo detect predict model=/Users/<USER>/Documents/Workspace/Models/AEC_Pack/PACK_AOI_best.pt source='/Users/<USER>/Documents/Samples/B465 AOI cosmetic inspection picture after UV Glue/7.11/漏胶'

yolo task=detect mode=predict model=/Users/<USER>/Documents/Workspace/Models/AEC_Pack/PACK_AOI_0805.pt source= G:\GGEC.AIAOI\GGEC.AOI\GGEC.AOI.T68ZJ.Client\bin\Debug\net6.0-windows\output\images\20250715195008_0_origin.jpg project=./YOLO_outputs/predict name=my_predict_07152003

# yolo detect val model=/Users/<USER>/Documents/20250630pm_reannotated_yolo11n_100.pt data=/Users/<USER>/Documents/Workspace/line_detect/projects/test003/test003.yaml project=./YOLO_outputs name=20250630pm_reannotated_yolo11n_100_val

yolo task=detect mode=train model=yolov8n.pt data=/Users/<USER>/Documents/Workspace/line_detect/projects/test004/test004.yaml epochs=300 imgsz=640 batch=16 project=./YOLO_outputs name=my_experiment_$(date +%Y%m%d_%H%M%S) 
yolo task=detect mode=train model=yolo11n-seg.pt data=/data/train/projects/pin_detect_seg/pin_detect_seg.yaml epochs=300 imgsz=640 batch=16 project=/data/train/YOLO_outputs name=my_experiment_$(date +%Y%m%d%H%M%S) 

# yolo task=detect mode=train model=yolov8n.pt data=/root/test002/test002.yaml epochs=100 imgsz=640 batch=16

/Users/<USER>/Documents/Workspace/line_detect/YOLO_outputs/20250701_121157/weights/best.pt


 yolo task=detect mode=train model=yolo11n.pt data=/data/train/projects/t68zj_0722_2046_dataset/t68zj_0722_2046_dataset.yaml epochs=300 batch=16 project=./YOLO_outputs name=train_task_$(date +%Y%m%d_%H%M%S) 
 yolo task=detect mode=train model=yolo11n.pt data=./projects/t68zj_0722_2020_dataset/t68zj_0722_2020_dataset.yaml epochs=300 imgsz=640 batch=16 project=./YOLO_outputs name=train_task_$(date +%Y%m%d%H%M%S) 
 
/Users/<USER>/Documents/Workspace/Models/AEC_Pack/PACK_AOI_best.pt
 yolo task=detect mode=train model=/root/workspace/vaildenv/PACK_AOI_0731.pt data=/root/workspace/vaildenv/dataset_aec_pack_0731/dataset_aec_pack_0731.yaml epochs=300 imgsz=640 batch=16 project=./YOLO_output name=train_task_$(date +%Y%m%d_%H%M%S) 

 yolo task=detect mode=predict model=/Users/<USER>/Documents/Workspace/Models/AEC_Pack/PACK_AOI_0805.pt source=/Users/<USER>/Documents/Samples/AEC_PACK/PACK_SYNC/original/2025/07/30 project=./YOLO_outputs

yolo task=detect mode=train model=/root/workspace/vaildenv/PACK_AOI_0731.pt data=/root/workspace/vaildenv/dataset_aec_pack_0731/dataset_aec_pack_0731.yaml epochs=300 imgsz=640 batch=16 lr0=0.001 project=./YOLO_output name=train_task_$(date +%Y%m%d_%H%M%S) 

yolo task=detect mode=train model=yolo11n.pt data=dataset_aec_pack_0805_1321/dataset_aec_pack_0805_1321.yaml epochs=300 imgsz=640 batch=16 project=./YOLO_output name=train_task_$(date +%Y%m%d_%H%M%S)

 yolo task=detect mode=train model=yolo11n.pt data=dataset_aec_pack_0805_1307_mix/dataset_aec_pack_0805_1307_mix.yaml epochs=300 imgsz=640 batch=16 project=./YOLO_output name=train_task_$(date +%Y%m%d_%H%M%S) pretrained=/data/train/pretrain_weight/t68zj_0721_best.pt

 yolo task=detect mode=train model=yolo11n.pt data=dataset_aec_pack_0805_1307_mix/dataset_aec_pack_0805_1307_mix.yaml epochs=300 imgsz=640 batch=16 project=./YOLO_output name=train_task_$(date +%Y%m%d_%H%M%S)

 yolo task=detect mode=train data=./dataset_20250724_191655/dataset_20250724_191655.yaml epochs=300 imgsz=640 batch=16 project=./YOLO_output name=train_task_$(date +%Y%m%d_%H%M%S) pretrained=/data/train/pretrain_weight/t68zj_0721_best.pt


yolo task=detect mode=train model=yolo11n.pt data=/root/workspace/vaildenv/dataset_aec_pack_0805_mix/dataset_aec_pack_0805_mix.yaml epochs=300 imgsz=640 batch=16 project=./YOLO_output name=train_task_$(date +%Y%m%d_%H%M%S)

 yolo task=detect mode=train model=/data/train/pretrain_weight/t68zj_0721_best.pt data=/data/train/projects/t68zj_0723_1059_dataset/t68zj_0723_1059_dataset.yaml epochs=300 imgsz=640 batch=16 project=./YOLO_output name=train_task_$(date +%Y%m%d_%H%M%S)